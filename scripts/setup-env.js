/**
 * 环境配置脚本
 * 🎯 核心价值：根据环境自动配置数据库和Prisma schema
 * 🔄 智能切换：开发环境SQLite，生产环境PostgreSQL
 * ⚡ 自动化：减少手动配置错误
 */

const fs = require('fs');
const path = require('path');

// 环境类型
const ENV_TYPES = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
};

// 数据库配置
const DB_CONFIGS = {
  [ENV_TYPES.DEVELOPMENT]: {
    provider: 'sqlite',
    url: 'file:./dev.db',
    description: 'SQLite开发数据库'
  },
  [ENV_TYPES.PRODUCTION]: {
    provider: 'postgresql',
    url: 'env("DATABASE_URL")',
    description: 'PostgreSQL生产数据库'
  },
  [ENV_TYPES.TEST]: {
    provider: 'sqlite',
    url: 'file:./test.db',
    description: 'SQLite测试数据库'
  }
};

// 获取当前环境
function getCurrentEnvironment() {
  const nodeEnv = process.env.NODE_ENV || ENV_TYPES.DEVELOPMENT;
  const vercelEnv = process.env.VERCEL_ENV;

  if (vercelEnv === 'production' || nodeEnv === 'production') {
    return ENV_TYPES.PRODUCTION;
  }

  if (nodeEnv === 'test') {
    return ENV_TYPES.TEST;
  }

  return ENV_TYPES.DEVELOPMENT;
}

// 读取Prisma schema文件
function readPrismaSchema() {
  const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
  
  if (!fs.existsSync(schemaPath)) {
    throw new Error('Prisma schema文件不存在');
  }
  
  return fs.readFileSync(schemaPath, 'utf8');
}

// 更新Prisma schema的数据库配置
function updatePrismaSchema(content, dbConfig) {
  // 替换datasource db块
  const datasourceRegex = /datasource db \{[\s\S]*?\}/;
  
  const newDatasource = `datasource db {
  provider = "${dbConfig.provider}"
  url      = ${dbConfig.url.startsWith('env(') ? dbConfig.url : `"${dbConfig.url}"`}
}`;
  
  return content.replace(datasourceRegex, newDatasource);
}

// 写入Prisma schema文件
function writePrismaSchema(content) {
  const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
  fs.writeFileSync(schemaPath, content, 'utf8');
}

// 创建环境特定的.env文件
function createEnvFile(environment, dbConfig) {
  const envContent = `# 环境配置 - ${environment.toUpperCase()}
# 🎯 自动生成，请勿手动编辑

# === 环境标识 ===
NODE_ENV=${environment}
NEXT_PUBLIC_ENV=${environment}

# === 数据库配置 ===
# ${dbConfig.description}
${dbConfig.provider === 'sqlite' ? 
  `DATABASE_URL="${dbConfig.url}"` : 
  `# DATABASE_URL 将由Vercel自动设置
# 本地测试PostgreSQL: DATABASE_URL="postgresql://username:password@localhost:5432/cube1_group?schema=public"`
}

# === 应用配置 ===
NEXTAUTH_SECRET=${environment === 'production' ? 'CHANGE_THIS_IN_PRODUCTION' : 'dev-secret-key'}
NEXTAUTH_URL=${environment === 'production' ? 'https://your-domain.vercel.app' : 'http://localhost:3000'}

# === 功能开关 ===
NEXT_PUBLIC_ENABLE_API=true
NEXT_PUBLIC_ENABLE_MIGRATION=${environment === 'development' ? 'true' : 'false'}
NEXT_PUBLIC_ENABLE_DEV_TOOLS=${environment === 'development' ? 'true' : 'false'}

# === 性能配置 ===
NEXT_PUBLIC_API_TIMEOUT=${environment === 'production' ? '15000' : '10000'}
NEXT_PUBLIC_SYNC_INTERVAL=${environment === 'production' ? '60000' : '30000'}
`;

  const envFileName = environment === 'production' ? '.env.production' : '.env.local';
  fs.writeFileSync(envFileName, envContent);
  
  return envFileName;
}

// 显示配置信息
function showConfigInfo(environment, dbConfig, envFile) {
  console.log(`🎯 环境配置完成: ${environment.toUpperCase()}`);
  console.log(`📊 数据库: ${dbConfig.description}`);
  console.log(`📁 配置文件: ${envFile}`);
  console.log('');
  
  if (environment === ENV_TYPES.PRODUCTION) {
    console.log('🚀 生产环境配置提醒:');
    console.log('   1. 确保在Vercel中设置了DATABASE_URL环境变量');
    console.log('   2. 更新NEXTAUTH_SECRET为安全的随机字符串');
    console.log('   3. 设置正确的NEXTAUTH_URL域名');
    console.log('   4. 运行数据库迁移: npx prisma migrate deploy');
    console.log('');
  } else if (environment === ENV_TYPES.DEVELOPMENT) {
    console.log('🛠️ 开发环境配置提醒:');
    console.log('   1. 运行 pnpm run db:push 同步数据库');
    console.log('   2. 运行 pnpm run db:seed 初始化数据');
    console.log('   3. 使用 pnpm run dev 启动开发服务器');
    console.log('');
  }
}

// 主函数
function main() {
  try {
    console.log('🔧 开始环境配置...\n');
    
    const environment = getCurrentEnvironment();
    const dbConfig = DB_CONFIGS[environment];
    
    console.log(`🌍 检测到环境: ${environment}`);
    console.log(`🗄️ 数据库配置: ${dbConfig.description}\n`);
    
    // 更新Prisma schema
    console.log('📝 更新Prisma schema...');
    const schemaContent = readPrismaSchema();
    const updatedSchema = updatePrismaSchema(schemaContent, dbConfig);
    writePrismaSchema(updatedSchema);
    console.log('✅ Prisma schema更新完成\n');
    
    // 创建环境配置文件
    console.log('📄 创建环境配置文件...');
    const envFile = createEnvFile(environment, dbConfig);
    console.log(`✅ 环境配置文件创建完成: ${envFile}\n`);
    
    // 显示配置信息
    showConfigInfo(environment, dbConfig, envFile);
    
  } catch (error) {
    console.error('❌ 环境配置失败:', error.message);
    process.exit(1);
  }
}

// 命令行参数处理
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('环境配置脚本');
    console.log('');
    console.log('用法:');
    console.log('  node scripts/setup-env.js [选项]');
    console.log('');
    console.log('选项:');
    console.log('  --help, -h     显示帮助信息');
    console.log('  --env <type>   强制指定环境类型 (development|production|test)');
    console.log('');
    console.log('环境变量:');
    console.log('  NODE_ENV       环境类型');
    console.log('  VERCEL_ENV     Vercel环境类型');
    process.exit(0);
  }
  
  // 强制指定环境
  const envIndex = args.indexOf('--env');
  if (envIndex !== -1 && args[envIndex + 1]) {
    const forcedEnv = args[envIndex + 1];
    if (Object.values(ENV_TYPES).includes(forcedEnv)) {
      process.env.NODE_ENV = forcedEnv;
      console.log(`🔧 强制设置环境: ${forcedEnv}\n`);
    } else {
      console.error(`❌ 无效的环境类型: ${forcedEnv}`);
      console.error(`   支持的类型: ${Object.values(ENV_TYPES).join(', ')}`);
      process.exit(1);
    }
  }
  
  main();
}

module.exports = {
  getCurrentEnvironment,
  updatePrismaSchema,
  createEnvFile,
  ENV_TYPES,
  DB_CONFIGS
};
